return {
  {
    "nvim-telescope/telescope.nvim",
    dependencies = { "nvim-lua/plenary.nvim" },
    config = function()
      require("telescope").setup({
        defaults = {
          vimgrep_arguments = {
            "C:/Users/<USER>/AppData/Local/nvim/deps/rg.exe",
            "--color=never",
            "--no-heading",
            "--with-filename",
            "--line-number",
            "--column",
            "--smart-case",
            "--threads=0",
          },
          find_command = {
            "C:/Users/<USER>/AppData/Local/nvim/deps/fd.exe",
            "--type",
            "f",
            "--strip-cwd-prefix",
          },
        },
      })
    end,
  },
  {
    "shortcuts/no-neck-pain.nvim",
    config = function()
      local screen_width = vim.api.nvim_get_option("columns")
      local precentage_of_screen = screen_width * 0.85
      require("no-neck-pain").setup({
        width = precentage_of_screen,
        side_buffers = {
          left = { enabled = true, char = ' ' },
          right = { enabled = true, char = ' ' },
        },
        autocmds = {
          enableOnVimEnter = true,
          enableOnTabEnter = true,
        },
      })
    end,
  },
  {
    "RRethy/vim-illuminate",
  },
  {
    "mikavilpas/yazi.nvim",
    config = function()
      require("yazi").setup()
    end,
  },
  {
    "ahkohd/buffer-sticks.nvim",
    config = function()
      require("buffer-sticks").setup({
        transparent = true,
        highlights = {
          active = { link = "Statement" },
          inactive = { link = "Whitespace" },
          label = { link = "Comment" },
        },
      })
      require("buffer-sticks").show()
    end,
  },
}