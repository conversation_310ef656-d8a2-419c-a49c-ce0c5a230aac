local M = {}

M.setup = function()
  local nnp = require("no-neck-pain")
  
  nnp.setup({
    -- Set a fixed width instead of calculating percentage
    width = 120,
    
    -- Disable automatic enabling to avoid conflicts with NvChad
    autocmds = {
      enableOnVimEnter = false, -- Changed to false to avoid conflicts
      enableOnTabEnter = false, -- Changed to false to avoid conflicts
      skipEnteringNoNeckPainBuffer = true,
      reloadOnColorSchemeChange = true,
    },
    
    -- Configure side buffers
    buffers = {
      colors = {
        background = "tokyonight", -- Use your colorscheme
        blend = -0.2,
      },
      left = {
        enabled = true,
      },
      right = {
        enabled = true,
      },
      bo = {
        filetype = "no-neck-pain",
        buftype = "nofile",
        bufhidden = "hide",
        buflisted = false,
        swapfile = false,
      },
      wo = {
        cursorline = false,
        cursorcolumn = false,
        colorcolumn = "0",
        foldcolumn = "0",
        list = false,
        number = false,
        relativenumber = false,
        spell = false,
        wrap = false,
      },
    },
    
    -- Integration settings
    integrations = {
      NvimTree = {
        position = "left",
        reopen = true,
      },
      NeoTree = {
        position = "left",
        reopen = true,
      },
    },
  })
  
  -- Create user commands for manual control
  vim.api.nvim_create_user_command("NoNeckPainToggle", function()
    nnp.toggle()
  end, { desc = "Toggle No Neck Pain" })
  
  vim.api.nvim_create_user_command("NoNeckPainEnable", function()
    nnp.enable()
  end, { desc = "Enable No Neck Pain" })
  
  vim.api.nvim_create_user_command("NoNeckPainDisable", function()
    nnp.disable()
  end, { desc = "Disable No Neck Pain" })
end

return M
