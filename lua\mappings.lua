require "nvchad.mappings"

-- add yours here

local map = vim.keymap.set

map("n", ";", ":", { desc = "CMD enter command mode" })
map("i", "jk", "<ESC>")

-- No Neck Pain keymaps
map("n", "<leader>np", "<cmd>NoNeckPainToggle<cr>", { desc = "Toggle No Neck Pain" })
map("n", "<leader>ne", "<cmd>NoNeckPainEnable<cr>", { desc = "Enable No Neck Pain" })
map("n", "<leader>nd", "<cmd>NoNeckPainDisable<cr>", { desc = "Disable No Neck Pain" })

-- map({ "n", "i", "v" }, "<C-s>", "<cmd> w <cr>")
