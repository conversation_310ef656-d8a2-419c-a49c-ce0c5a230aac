-- Bootstrap lazy.nvim
local lazypath = vim.fn.stdpath("data") .. "/lazy/lazy.nvim"
if not vim.loop.fs_stat(lazypath) then
  vim.fn.system({
    "git",
    "clone",
    "--filter=blob:none",
    "https://github.com/folke/lazy.nvim.git",
    "--branch=stable",
    lazypath,
  })
end
vim.opt.rtp:prepend(lazypath)

-- Load configurations
require("options")
require("keymaps")
require("lazy").setup({
  { import = "plugins" },
  { import = "plugins.lsp" },
})
require("themes")

-- Hot reload config on file changes
local config_path = vim.fn.stdpath('config')
local watcher = vim.loop.new_fs_event()
watcher:start(config_path, {}, function(err, filename, events)
  if err then return end
  if filename and filename:match('%.lua$') then
    vim.schedule(function()
      vim.notify('Config changed: ' .. filename .. ', reloading...')
      if filename == 'init.lua' then
        vim.cmd('source ' .. config_path .. '/init.lua')
      else
        local module = filename:gsub('%.lua$', ''):gsub('/', '.'):gsub('lua%.', '')
        if package.loaded[module] then
          package.loaded[module] = nil
          require(module)
        end
      end
    end)
  end
end)

print("Neovim config loaded successfully")