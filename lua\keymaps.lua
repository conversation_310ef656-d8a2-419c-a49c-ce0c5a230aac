-- Key mappings
vim.g.mapleader = " "

-- File operations
vim.keymap.set('n', '<leader>w', ':w<CR>', { desc = 'Save file' })
vim.keymap.set('n', '<leader>q', ':q<CR>', { desc = 'Quit' })

-- Search and navigation
vim.keymap.set('n', '<leader>ff', ':Telescope find_files<CR>', { desc = 'Find files' })
vim.keymap.set('n', '<leader>fg', ':Telescope live_grep<CR>', { desc = 'Live grep' })

-- Windows and splits
vim.keymap.set('n', '<leader>v', ':vsplit<CR>', { desc = 'Vertical split' })
vim.keymap.set('n', '<leader>s', ':split<CR>', { desc = 'Horizontal split' })
vim.keymap.set('n', '<C-h>', '<C-w>h', { desc = 'Move to left window' })
vim.keymap.set('n', '<C-j>', '<C-w>j', { desc = 'Move to bottom window' })
vim.keymap.set('n', '<C-k>', '<C-w>k', { desc = 'Move to top window' })
vim.keymap.set('n', '<C-l>', '<C-w>l', { desc = 'Move to right window' })

-- Buffers
vim.keymap.set('n', '<leader>bn', ':bnext<CR>', { desc = 'Next buffer' })
vim.keymap.set('n', '<leader>bp', ':bprev<CR>', { desc = 'Previous buffer' })
vim.keymap.set('n', '<leader>bd', ':bdelete<CR>', { desc = 'Delete buffer' })
vim.keymap.set('n', '<leader>bj', function() require("buffer-sticks").jump() end, { desc = 'Buffer jump mode' })

-- Explorer and terminal
vim.keymap.set('n', '<leader>e', ':Yazi<CR>', { desc = 'Open yazi file manager' })
vim.keymap.set('n', '<leader>t', ':terminal<CR>', { desc = 'Open terminal' })

--Twilight Toggle
vim.keymap.set('n', '<leader>T', ':Twilight<CR>', { desc = 'Toggle twilight' })

-- LSP keymaps are set in lsp.lua on_attach