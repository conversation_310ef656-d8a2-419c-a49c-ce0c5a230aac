return {
  {
    "goolord/alpha-nvim",
    config = function()
      local alpha = require("alpha")
      local dashboard = require("alpha.themes.dashboard")

      -- Header
      dashboard.section.header.val = {
        " ███╗   ██╗███████╗ ██████╗ ██╗   ██╗██╗███╗   ███╗",
        " ████╗  ██║██╔════╝██╔═══██╗██║   ██║██║████╗ ████║",
        " ██╔██╗ ██║█████╗  ██║   ██║██║   ██║██║██╔████╔██║",
        " ██║╚██╗██║██╔══╝  ██║   ██║╚██╗ ██╔╝██║██║╚██╔╝██║",
        " ██║ ╚████║███████╗╚██████╔╝ ╚████╔╝ ██║██║ ╚═╝ ██║",
        " ╚═╝  ╚═══╝╚══════╝ ╚═════╝   ╚═══╝  ╚═╝╚═╝     ╚═╝",
        "",
        "Welcome to Neovim - Customized Experience",
      }

      -- System info
      local function get_system_info()
        local os_name = vim.fn.has("win32") and "Windows" or vim.fn.has("mac") and "macOS" or "Linux"
        local nvim_version = vim.version().major .. "." .. vim.version().minor .. "." .. vim.version().patch
        return "OS: " .. os_name .. " | Neovim: " .. nvim_version
      end

      dashboard.section.system_info = {
        type = "text",
        val = get_system_info(),
        opts = { hl = "Comment", position = "center" },
      }

      -- Keybinds section
       dashboard.section.keybinds = {
        type = "text",
        val = {
          "",
          "Keybinds:",
          "  <leader>w  - Save",
          "  <leader>q  - Quit",
          "  <leader>ff - Find files",
          "  <leader>fg - Live grep",
          "  <leader>   - Show menu",
        },
        opts = { hl = "String", position = "center" },
      }

      -- Buttons
      dashboard.section.buttons.val = {
        dashboard.button("e", "📄 New file", ":ene <BAR> startinsert <CR>"),
        dashboard.button("f", "🔍 Find file", ":Telescope find_files<CR>"),
        dashboard.button("r", "📂 Recent files", ":Telescope oldfiles<CR>"),
        dashboard.button("q", "❌ Quit", ":qa<CR>"),
      }

      -- Footer
      dashboard.section.footer.val = "Press <leader> to see all keybinds"

      -- Layout
      dashboard.config.layout = {
        { type = "padding", val = 2 },
        dashboard.section.header,
        { type = "padding", val = 1 },
        dashboard.section.system_info,
        { type = "padding", val = 1 },
        dashboard.section.keybinds,
        { type = "padding", val = 1 },
        dashboard.section.buttons,
        { type = "padding", val = 1 },
        dashboard.section.footer,
      }

      alpha.setup(dashboard.config)
    end,
  },
   {
     "folke/which-key.nvim",
     event = "VeryLazy",
     config = function()
       require("which-key").setup({
         triggers = { "<leader>" },
         win = {
           border = "rounded",
         },
       })
     end,
   },
   {
     "echasnovski/mini.icons",
     config = function()
       require("mini.icons").setup()
     end,
   },
    {
      "folke/twilight.nvim",
      config = function()
        require("twilight").setup({
          dimming = {
            alpha = 0.25,
            color = { "Normal", "#ffffff" },
          },
          context = 10,
          treesitter = true,
          expand = {
            "function",
            "method",
            "table",
            "if_statement",
          },
        })
      end,
    },
    {
      "patrickpichler/hovercraft.nvim",
      dependencies = { "nvim-lua/plenary.nvim" },
      event = "VeryLazy",
      config = function()
        vim.keymap.set('n', '<leader>h', function() require('hovercraft').hover() end, { desc = 'Show hover information' })
      end,
    },
}