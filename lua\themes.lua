-- Theme settings
vim.cmd.colorscheme('obscure')

-- Customize LSP floating window highlights for better visibility
-- These colors provide better contrast against the obscure theme
vim.api.nvim_set_hl(0, "NormalFloat", {
  bg = "#151515",  -- Darker background for better contrast
  fg = "#d16914"   -- Light text for readability
})

vim.api.nvim_set_hl(0, "FloatBorder", {
  fg = "#7aa2f7",  -- Blue border for visibility
  bg = "#2d2d2d"   -- Match the float background
})

-- Additional highlight groups for better LSP experience
vim.api.nvim_set_hl(0, "LspInfoBorder", {
  fg = "#7aa2f7",
  bg = "#2d2d2d"
})

-- Diagnostic floating window
vim.api.nvim_set_hl(0, "DiagnosticFloatingError", {
  fg = "#f7768e",
  bg = "#2d2d2d"
})

vim.api.nvim_set_hl(0, "DiagnosticFloatingWarn", {
  fg = "#e0af68",
  bg = "#2d2d2d"
})

vim.api.nvim_set_hl(0, "DiagnosticFloatingInfo", {
  fg = "#7dcfff",
  bg = "#2d2d2d"
})

vim.api.nvim_set_hl(0, "DiagnosticFloatingHint", {
  fg = "#9ece6a",
  bg = "#2d2d2d"
})