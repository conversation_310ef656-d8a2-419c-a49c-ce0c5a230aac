return {
  {
    "stevearc/conform.nvim",
    -- event = 'BufWritePre', -- uncomment for format on save
    opts = require "configs.conform",
  },

  -- These are some examples, uncomment them if you want to see them work!
  {
    "neovim/nvim-lspconfig",
    config = function()
      require "configs.lspconfig"
    end,
  },

  -- test new blink
  { import = "nvchad.blink.lazyspec" },

  {
    "shortcuts/no-neck-pain.nvim",
    event = "VeryLazy", -- Load after UI is initialized
    config = function()
      require("configs.no-neck-pain").setup()
    end,
  },

  -- {
  -- 	"nvim-treesitter/nvim-treesitter",
  -- 	opts = {
  -- 		ensure_installed = {
  -- 			"vim", "lua", "vimdoc",
  --      "html", "css", "javascript", "typescript", "tsx", "rust", "go", "elixir", "python",
  --      "markdown", "markdown_inline"
  -- 		},
  -- 	},
  -- },
}
